<template>
  <wd-floating-panel v-model:height="height" :anchors="anchors">
    <div class="flex back-eee all relative">
      <div class="f-1 overflow-auto">
        <scroll-view class="all" scroll-y @scrolltolower="scrollIntoViewId = null" :scroll-into-view="scrollIntoViewId">
          <div class="pad-24">
            <div v-if="detail">
              <div class="text-center mar-B16 fon-S24 color-666">{{ detail.CreateTime.slice(0, 16).replace('T', ' ') }}</div>
              <div class="border-R16 pad-32 back-white">
                <div class="fon-W600 fon-S40 mar-B12">{{ detail.FeedbackType }}</div>
                <div class="fon-S28">{{ detail.FeedbackContent }}</div>
                <div class="mar-T16 flex" v-if="detail.FeedbackImg.length > 0">
                  <template v-for="(url, index) in detail.FeedbackImg.split(',')" :key="index">
                    <image class="img mar-L12" @click="previewImage(detail.FeedbackImg.split(','), url)" v-if="url" :src="url"></image>
                  </template>
                </div>
              </div>

              <div class="mar-T24 border-R16 pad-32 back-white">
                <div class="color-666 fon-S26 mar-B16">管理员回复</div>
                <div>已经收到您的反馈，我们会尽快回复的哦！</div>
              </div>
            </div>

            <template v-for="(item, index) in replyList" :key="index">
              <div class="mar-Y24">
                <div class="fon-S24 text-center color-666">{{ item.CreateTime.slice(0, 16).replace('T', ' ') }}</div>
                <!-- 任务卡片 -->
                <div v-if="item.task" class="task-card">
                  <div class="task-header">
                    <div class="task-icon">
                      <wd-icon name="task" size="20px" color="#4A90E2"></wd-icon>
                    </div>
                    <div class="task-title">任务反馈</div>
                    <div class="task-status" :class="{ 'status-completed': item.perform, 'status-pending': !item.perform }">
                      <div class="status-dot"></div>
                      <span class="status-text">{{ item.perform ? '已处理' : '待处理' }}</span>
                    </div>
                  </div>

                  <div class="task-content">
                    <div class="content-text">{{ item.ReplyContent }}</div>

                    <div class="task-images" v-if="item.ReplyImg.length > 0">
                      <template v-for="(url, index) in item.ReplyImg.split(',')" :key="index">
                        <div class="image-wrapper" v-if="url">
                          <image class="task-image" @click="previewImage(item.ReplyImg.split(','), url)" :src="url" mode="aspectFill"></image>
                        </div>
                      </template>
                    </div>
                    <div v-if="item.perform && item.remark" class="remark-text">
                      <wd-icon name="info" size="14px" color="#999"></wd-icon>
                      <span>{{ item.remark }}</span>
                    </div>

                    <div class="task-footer">
                      <div class="user-info">
                        <div class="user-avatar">
                          <wd-icon name="user" size="16px" color="#666"></wd-icon>
                        </div>
                        <span class="user-name">{{ item.createUserInfo.Name }}</span>
                      </div>

                      <div class="task-actions">
                        <wd-button v-if="!item.perform && userInfo.station === '运营中心'" type="primary" size="small" class="reply-btn" @click="handleFeedbackClick(item)">
                          <wd-icon name="chat" size="14px"></wd-icon>
                          <span>回复</span>
                        </wd-button>
                        <div v-else class="fon-S24 color-999">{{ item.ModifyTime.slice(0, 16).replace('T', ' ') }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-else class="flex" :class="{ oneself: item.createUserInfo.id == userInfo.id }">
                  <div v-if="item.createUserInfo.id !== userInfo.id">
                    <div class="user_box mar-T16">
                      <wd-icon name="user" size="22px"></wd-icon>
                    </div>
                  </div>
                  <div class="flex f-column">
                    <div class="fon-S22 pad-12 inline-block color-666">{{ item.createUserInfo.id !== userInfo.id ? item.createUserInfo.Name : '' }}</div>
                    <div class="text_box inline-block back-white">
                      <div class="mar-T16 flex mar-B16" v-if="item.ReplyImg.length > 0">
                        <template v-for="(url, index) in item.ReplyImg.split(',')" :key="index">
                          <image class="img mar-L12" @click="previewImage(item.ReplyImg.split(','), url)" v-if="url" :src="url"></image>
                        </template>
                      </div>
                      <div class="fon-S28" :id="item.ReplyID">{{ item?.ReplyContent }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div style="height: 100px" id="bottomID"></div>
        </scroll-view>
        <div class="input_but back-white absolute">
          <wd-notice-bar custom-class=" mar-B16" text="当前编辑的内容将会创建一条修改任务" prefix="check-outline" type="info" v-if="reply.task" />

          <div class="flex mar-B16" v-if="images?.length > 0">
            <template v-for="(url, index) in images" :key="index">
              <div class="img_box relative">
                <div class="absolute img_icon">
                  <wd-icon @click="images.splice(index, 1)" name="close-circle-filled" color="#fc3434" size="18px"></wd-icon>
                </div>
                <image class="img" :src="url" @click="previewImage(images, url)" mode="aspectFill"></image>
              </div>
            </template>
          </div>
          <div class="flex relative">
            <!-- 创建修改任务 -->
            <div class="flex f-column-reverse mar-R12">
              <div :class="{ backBut: reply.task }" class="input_but1 back-eee pad-16 border-Rall" @click="reply.task = !reply.task"><wd-icon :color="`${reply.task ? '#fff' : '#666'}`" name="edit-1" size="18px" /></div>
            </div>
            <div class="flex f-column-reverse">
              <div class="input_but1 back-eee pad-16 border-Rall">
                <UploadImage url="https://www.szwgft.cn/nodeServer/resource/upload/feedback" @success="handleUploadSuccess">
                  <wd-icon name="picture" size="16px" color="#333"></wd-icon>
                </UploadImage>
              </div>
            </div>
            <div class="f-1 back-eee f-xy-center border-R12 mar-X24 pad-12">
              <textarea :cursor-spacing="30" class="W100 pad-L12 fon-S26" v-model="reply.ReplyContent" auto-height placeholder="请输入内容" />
            </div>
            <div class="flex f-column-reverse">
              <div class="input_but1 back-eee pad-16 border-Rall">
                <wd-icon name="check-bold" @click="createFeedbackIssueReply" size="18px" color="#333"></wd-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </wd-floating-panel>

  <wd-message-box />
</template>

<script setup>
import { ref, reactive, nextTick, watch } from 'vue'
import { useToast } from 'wot-design-uni'
import { cache } from '@/utils/cache.js'
import UploadImage from '@/components/UploadeImage/index.vue'

import * as Api from '@/services/model/feedback.issue.js'

import * as CommonApi from '@/services/model/common.js'
import { useMessage } from 'wot-design-uni'

const props = defineProps({ id: Number })
const emit = defineEmits(['close'])

const message = useMessage()
const toast = useToast()
let userInfo = null
const detail = ref(null)
const scrollIntoViewId = ref(null)
const height = ref(0)
const anchors = ref([0, uni.getSystemInfoSync().windowHeight])

watch(height, (val) => (val === 0 ? emit('close') : ''))

// 获取反馈详情
async function getFeedbackIssueDetail(id) {
  const { data } = await Api.feedbackIssueDetailApi(id ?? props.id)
  const res = await CommonApi.dictionaryLookup('feedback')
  data.FeedbackType = res.data.find((item) => item.DictCode == data.FeedbackType)?.DictValue
  detail.value = data
}

// 回复列表
const replyList = ref(null)
async function getFeedbackIssueList(id) {
  const { data } = await Api.feedbackIssueReplyListApi(id ?? FeedbackID.value ?? props.id)

  replyList.value = data
    .map((item) => {
      item.createUserInfo = JSON.parse(item.createUserInfo)[0]
      return item
    })
    .reverse()

  nextTick(() => (scrollIntoViewId.value = 'bottomID'))
  if (data.length) {
    await Api.feedbackReplyReadApi({ UserID: userInfo.id, ReplyID: data[0].ReplyID })
  }
}

const reply = reactive({ FeedbackID: null, ReplyContent: null, ReplyUserID: null, ReplyImg: '', task: false })

// 发送回复
async function createFeedbackIssueReply() {
  console.log(FeedbackID.value)

  if (!reply.ReplyContent) return toast.warning('请输入内容')
  reply.FeedbackID = FeedbackID.value ?? props.id
  reply.ReplyUserID = userInfo.id
  reply.ReplyImg = images.value.join(',')
  await Api.feedbackIssueReplyApi(reply)
  reply.ReplyContent = null
  reply.ReplyImg = ''
  images.value = []
  reply.task = false
  getFeedbackIssueList()
}

// 处理错误
function handleFeedbackClick(item) {
  const data = { ...item }
  message.prompt({ title: '请输入备注消息' }).then(async ({ value }) => {
    data.remark = value
    data.perform = true
    await Api.feedbackIssueReplyUpdateApi(data)
    getFeedbackIssueList()
  })
}

function previewImage(urls, current) {
  uni.previewImage({ urls, current })
}

const images = ref([])
function handleUploadSuccess(e) {
  images.value.push(e.data)
}

const FeedbackID = ref(null)
function getFeedbackDetail(id) {
  FeedbackID.value = id
  if (height.value != 0) {
    return (height.value = 0)
  }
  height.value = anchors.value[1]
  userInfo = cache.get('userInfo')
  getFeedbackIssueList(id)
  getFeedbackIssueDetail(id)
}

function getFeedbackIsOpen() {
  return height.value !== 0
}
function close() {
  height.value = 0
}

defineExpose({ getFeedbackDetail, getFeedbackIsOpen, close })
</script>

<style lang="less" scoped>
.img {
  width: 100rpx;
  height: 100rpx;
}

.backBut {
  background-color: #38a169;
}

.input_but {
  left: 0;
  right: 0;
  bottom: 10px;
  border-radius: 12rpx 12rpx 0 0;
  padding: 20rpx 28rpx 30rpx 28rpx;
}
.input_but1 {
  display: inline-block;
  // width: 32rpx;
  // height: 32rpx;
}

.text_box {
  display: inline-block;
  padding: 18rpx 24rpx;
  border-radius: 8rpx;
  max-width: 480rpx;
}
.user_box {
  background-color: #fff;
  display: inline-block;
  padding: 12rpx;
  border-radius: 100%;
  margin-right: 20rpx;
}

.inline-block {
  display: inline-block;
}
.oneself {
  flex-direction: row-reverse;
  .user_box {
    margin-left: 20rpx !important;
  }
  .text_box {
    background-color: #95ec69;
  }
}

.img_box {
  width: 90rpx;
  height: 90rpx;
  margin: 0 12rpx;
  border: 2rpx solid #e5e5e5;
  .img {
    width: 90rpx;
    height: 90rpx;
  }
  .img_icon {
    right: -15rpx;
    top: -10rpx;
    z-index: 9;
  }
}

.pack-up {
  right: 4rpx;
  top: -70rpx;
}

/* 任务卡片样式 */
.task-card {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 16rpx 0;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.task-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 12rpx;
}

.task-icon {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a202c;
  flex: 1;
}

.task-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.status-completed {
  background: rgba(72, 187, 120, 0.1);
  color: #38a169;
}

.status-pending {
  background: rgba(237, 137, 54, 0.1);
  color: #dd6b20;
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: currentColor;
}

.task-content {
  margin-top: 16rpx;
}

.content-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.task-images {
  display: flex;
  gap: 12rpx;
  margin: 16rpx 0;
  flex-wrap: wrap;
}

.image-wrapper {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.task-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  transition: transform 0.2s ease;
}

.task-image:active {
  transform: scale(0.95);
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 20rpx;
  gap: 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.user-avatar {
  width: 32rpx;
  height: 32rpx;
  background: #f7fafc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e2e8f0;
}

.user-name {
  font-size: 24rpx;
  color: #718096;
  font-weight: 500;
}

.task-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.remark-text {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #999;
  background: #f8f9fa;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.reply-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 12rpx 20rpx !important;
  border-radius: 20rpx !important;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
  border: none !important;
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.3);
  transition: all 0.2s ease;
}

.reply-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(74, 144, 226, 0.3);
}
</style>

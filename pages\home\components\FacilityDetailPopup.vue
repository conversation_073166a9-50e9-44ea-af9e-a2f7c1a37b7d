<template>
  <div class="facility-popup-container" v-if="facilityData">
    <!-- 弹窗标题 -->
    <div class="facility-popup-header">
      <div class="header-icon">🏗️</div>
      <div class="header-text">
        <h3>{{ facilityData.key }}</h3>
        <p>设施详细数据统计</p>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="facility-popup-content">
      <div class="facility-table">
        <!-- 表格头部 -->
        <div class="table-header">
          <div class="header-cell type-header">
            <span class="header-icon">📋</span>
            <span class="header-label">类型</span>
          </div>
          <div class="header-cell municipal-header">
            <span class="header-text">市政</span>
            <span class="header-unit">个</span>
          </div>
          <div class="header-cell plot-header">
            <span class="header-text">小区</span>
            <span class="header-unit">个</span>
          </div>
          <div class="header-cell total-header">
            <span class="header-text">合计</span>
            <span class="header-unit">个</span>
          </div>
        </div>

        <!-- 数据行 -->
        <div v-for="(item, index) in facilityData.data" :key="index" class="table-row" :class="{ 'row-even': index % 2 === 0 }" v-show="item.data.plot > 0 || item.data.municipal > 0">
          <div class="data-cell type-cell">
            <span class="type-icon">{{ getFacilityIcon(item.subtype) }}</span>
            <span class="type-name">{{ item.subtype }}</span>
          </div>
          <div class="data-cell value-cell municipal-cell">
            <span class="value-number">{{ item.data.municipal.toLocaleString() }}</span>
          </div>
          <div class="data-cell value-cell plot-cell">
            <span class="value-number">{{ item.data.plot.toLocaleString() }}</span>
          </div>
          <div class="data-cell total-cell">
            <span class="total-number">{{ item.data.total.toLocaleString() }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 空数据提示 -->
    <div class="empty-data" v-if="!facilityData.data || facilityData.data.length === 0">
      <div class="empty-icon">📋</div>
      <div class="empty-text">暂无数据</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  facilityData: {
    type: Object,
    default: () => null
  }
})

// 获取设施类型图标
const getFacilityIcon = (subtype) => {
  const iconMap = {
    单箅: '🔲',
    双箅: '🔳',
    多箅: '⬛',
    检查井: '🕳️',
    雨水口: '💧',
    污水井: '🌊',
    化粪池: '🏭',
    泵站: '⚙️',
    闸门: '🚪',
    管件: '🔧',
    其他: '📦'
  }
  return iconMap[subtype] || '📋'
}
</script>

<style lang="less" scoped>
// 设施弹窗样式
.facility-popup-container {
  padding: 32rpx;
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  height: 100%;
  overflow-y: auto;
}

.facility-popup-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 16rpx;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(79, 172, 254, 0.3);

  .header-icon {
    font-size: 48rpx;
    margin-right: 20rpx;
    filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  }

  .header-text {
    flex: 1;

    h3 {
      font-size: 36rpx;
      font-weight: 700;
      margin: 0 0 8rpx 0;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }

    p {
      font-size: 24rpx;
      margin: 0;
      opacity: 0.9;
      font-weight: 400;
    }
  }
}

.facility-popup-content {
  .facility-table {
    background: white;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid #e8f4fd;

    .table-header {
      background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);
      border-bottom: 2rpx solid #d6ebf5;
      min-height: 80rpx;
      display: flex;

      .header-cell {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20rpx 16rpx;
        font-weight: 600;
        font-size: 24rpx;
        border-right: 1rpx solid #d6ebf5;
        gap: 8rpx;

        &:last-child {
          border-right: none;
        }

        .header-icon {
          font-size: 20rpx;
        }

        .header-label,
        .header-text {
          font-weight: 600;
          line-height: 1.2;
        }

        .header-unit {
          font-size: 18rpx;
          opacity: 0.8;
          font-weight: 500;
          background: rgba(255, 255, 255, 0.6);
          padding: 2rpx 6rpx;
          border-radius: 6rpx;
        }

        &.type-header {
          width: 140rpx;
          flex: none;
          background: linear-gradient(135deg, #4facfe 0%, rgba(79, 172, 254, 0.8) 100%);
          color: white;
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
        }

        &.municipal-header {
          background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
          color: #1976d2;
        }

        &.plot-header {
          background: linear-gradient(135deg, #fce4ec 0%, #fff0f5 100%);
          color: #6c80e8;
        }

        &.total-header {
          background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
          color: #388e3c;
        }
      }
    }

    .table-row {
      border-bottom: 1rpx solid #f0f8ff;
      min-height: 60rpx;
      display: flex;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
        transform: translateY(-1rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }

      &.row-even {
        background: linear-gradient(135deg, #fafbff 0%, #f8fbff 100%);
      }

      .data-cell {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16rpx 12rpx;
        border-right: 1rpx solid #f0f8ff;

        &:last-child {
          border-right: none;
        }

        &.type-cell {
          width: 140rpx;
          flex: none;
          background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
          font-weight: 600;
          color: #34495e;
          border-right: 1rpx solid #e8f4fd;
          gap: 8rpx;

          .type-icon {
            font-size: 20rpx;
          }

          .type-name {
            font-size: 24rpx;
            font-weight: 600;
            line-height: 1.2;
          }
        }

        &.value-cell {
          .value-number {
            font-size: 26rpx;
            font-weight: 600;
            font-family: 'Courier New', monospace;
          }

          &.municipal-cell {
            background: linear-gradient(135deg, #f3f8ff 0%, #e8f4fd 100%);
            color: #1976d2;
          }

          &.plot-cell {
            background: linear-gradient(135deg, #fff0f5 0%, #fce4ec 100%);
            color: #6c80e8;
          }
        }

        &.total-cell {
          background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
          font-weight: 600;
          color: #27ae60;

          .total-number {
            font-size: 28rpx;
            font-weight: 700;
            font-family: 'Courier New', monospace;
          }
        }
      }
    }
  }
}

// 空数据提示
.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  color: #95a5a6;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 16rpx;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 28rpx;
    font-weight: 500;
  }
}
</style>

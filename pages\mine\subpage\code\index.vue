<template>
  <view class="charts-box box-shadow">
    <qiun-data-charts type="bar" :opts="opts" :chartData="chartData" />
  </view>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
// import qi from '@/uni_modules/'

const opts = {
  color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
  padding: [15, 30, 0, 5],
  enableScroll: false,
  legend: {},
  xAxis: { boundaryGap: 'justify', disableGrid: false, min: 0, axisLine: false, max: 70 },
  yAxis: {},
  extra: { bar: { type: 'stack', width: 30, meterBorde: 1, meterFillColor: '#FFFFFF', activeBgColor: '#000000', activeBgOpacity: 0.08, categoryGap: 2 } }
}

const chartData = {
  categories: ['2018', '2019', '2020', '2021', '2022', '2023'],
  series: [
    { name: '目标值', data: [35, 36, 31, 33, 13, 34] },
    { name: '完成量', data: [18, 27, 21, 24, 6, 28] }
  ]
}
</script>

<style lang="less" scoped>
.charts-box {
  height: 300rpx;
  box-sizing: border-box;
  margin: 16rpx;
}
</style>

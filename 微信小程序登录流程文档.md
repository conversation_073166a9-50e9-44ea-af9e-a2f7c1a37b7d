# 微信小程序登录流程详解

## 概述

微信小程序的登录机制是基于微信官方提供的登录体系，通过获取用户的临时登录凭证（code）来实现用户身份验证。整个流程涉及小程序前端、开发者服务器和微信服务器三方交互。

## 登录流程图

```
小程序端          开发者服务器          微信服务器
    |                  |                  |
    |-- wx.login() --->|                  |
    |                  |                  |
    |<-- 返回 code -----|                  |
    |                  |                  |
    |-- 发送 code ----->|                  |
    |                  |                  |
    |                  |-- code2Session ->|
    |                  |                  |
    |                  |<-- openid + ---- |
    |                  |    session_key   |
    |                  |                  |
    |<-- 返回登录态 ----|                  |
    |    (token等)      |                  |
```

## 详细步骤

### 1. 小程序端获取登录凭证

```javascript
// 调用 wx.login 获取临时登录凭证
wx.login({
  success: function(res) {
    if (res.code) {
      // 发送 res.code 到后台换取 openId, sessionKey, unionId
      console.log('登录凭证：', res.code);
      // 将 code 发送给开发者服务器
      sendCodeToServer(res.code);
    } else {
      console.log('登录失败！' + res.errMsg);
    }
  }
});
```

### 2. 发送 code 到开发者服务器

```javascript
function sendCodeToServer(code) {
  wx.request({
    url: 'https://your-server.com/api/login',
    method: 'POST',
    data: {
      code: code
    },
    success: function(res) {
      if (res.data.success) {
        // 登录成功，保存登录态
        wx.setStorageSync('token', res.data.token);
        wx.setStorageSync('openid', res.data.openid);
      }
    }
  });
}
```

### 3. 服务器端验证登录凭证

开发者服务器需要调用微信的 `code2Session` 接口来验证登录凭证：

```javascript
// Node.js 示例
const axios = require('axios');

async function verifyWechatLogin(code) {
  const appId = 'your_app_id';
  const appSecret = 'your_app_secret';
  
  try {
    const response = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
      params: {
        appid: appId,
        secret: appSecret,
        js_code: code,
        grant_type: 'authorization_code'
      }
    });
    
    const { openid, session_key, unionid, errcode, errmsg } = response.data;
    
    if (errcode) {
      throw new Error(`微信登录验证失败: ${errmsg}`);
    }
    
    // 返回用户信息
    return {
      openid,
      session_key,
      unionid
    };
  } catch (error) {
    console.error('验证微信登录失败:', error);
    throw error;
  }
}
```

## 获取用户信息

### 使用 wx.getUserProfile（推荐）

从基础库 2.10.4 开始，微信官方推荐使用 `wx.getUserProfile` 替代 `wx.getUserInfo`：

```javascript
// 获取用户信息
getUserProfile() {
  wx.getUserProfile({
    desc: '用于完善会员资料', // 声明获取用户个人信息后的用途
    success: (res) => {
      console.log('用户信息：', res.userInfo);
      // 可以将用户信息发送给后台保存
      this.saveUserInfo(res.userInfo);
    },
    fail: (err) => {
      console.log('获取用户信息失败：', err);
    }
  });
}
```

### 完整的登录+获取用户信息流程

```javascript
// 完整登录流程
login() {
  // 1. 先获取登录凭证
  wx.login({
    success: (loginRes) => {
      if (loginRes.code) {
        // 2. 获取用户信息
        wx.getUserProfile({
          desc: '用于完善会员资料',
          success: (profileRes) => {
            // 3. 将登录凭证和用户信息一起发送给服务器
            this.sendLoginData({
              code: loginRes.code,
              userInfo: profileRes.userInfo,
              encryptedData: profileRes.encryptedData,
              iv: profileRes.iv
            });
          }
        });
      }
    }
  });
},

sendLoginData(data) {
  wx.request({
    url: 'https://your-server.com/api/login',
    method: 'POST',
    data: data,
    success: (res) => {
      if (res.data.success) {
        // 登录成功
        wx.setStorageSync('userInfo', res.data.userInfo);
        wx.setStorageSync('token', res.data.token);
        
        // 跳转到主页面
        wx.switchTab({
          url: '/pages/index/index'
        });
      }
    }
  });
}
```

## 重要注意事项

### 1. code 的有效期
- 登录凭证 `code` 只能使用一次，且有效期为 5 分钟
- 每次调用 `wx.login()` 都会使之前的 `code` 失效

### 2. session_key 的管理
- `session_key` 用于解密用户数据，需要在服务器端妥善保存
- 不要将 `session_key` 传输到前端
- 当用户重新登录时，`session_key` 会发生变化

### 3. 用户信息获取的变化
- 从 2021年4月开始，`wx.getUserInfo` 不再弹出授权弹窗
- 必须使用 `wx.getUserProfile` 来获取用户信息
- 每次调用 `wx.getUserProfile` 都会弹出授权弹窗

### 4. 登录态维护
```javascript
// 检查登录态是否过期
wx.checkSession({
  success: function() {
    // session_key 未过期，并且在本生命周期一直有效
    console.log('登录态有效');
  },
  fail: function() {
    // session_key 已经失效，需要重新执行登录流程
    console.log('登录态失效，需要重新登录');
    this.login();
  }
});
```

## 最佳实践

### 1. 静默登录
在小程序启动时进行静默登录，只获取 openid，不获取用户信息：

```javascript
// app.js
App({
  onLaunch: function() {
    // 静默登录
    this.silentLogin();
  },
  
  silentLogin() {
    wx.login({
      success: (res) => {
        if (res.code) {
          // 发送 code 到服务器，只获取 openid
          wx.request({
            url: 'https://your-server.com/api/silent-login',
            method: 'POST',
            data: { code: res.code },
            success: (serverRes) => {
              if (serverRes.data.success) {
                wx.setStorageSync('openid', serverRes.data.openid);
              }
            }
          });
        }
      }
    });
  }
});
```

### 2. 按需获取用户信息
只在真正需要用户信息时才调用 `wx.getUserProfile`：

```javascript
// 在需要用户信息的页面
needUserInfo() {
  const userInfo = wx.getStorageSync('userInfo');
  if (!userInfo) {
    // 提示用户需要授权
    wx.showModal({
      title: '提示',
      content: '需要获取您的用户信息',
      success: (res) => {
        if (res.confirm) {
          this.getUserProfile();
        }
      }
    });
  }
}
```

## 错误处理

### 常见错误码
- `40029`: code 无效
- `45011`: 频率限制，每个用户每分钟100次
- `40013`: 不合法的 AppID

### 错误处理示例
```javascript
// 服务器端错误处理
if (response.data.errcode) {
  switch (response.data.errcode) {
    case 40029:
      throw new Error('登录凭证无效');
    case 45011:
      throw new Error('请求过于频繁，请稍后再试');
    case 40013:
      throw new Error('AppID 配置错误');
    default:
      throw new Error(`登录失败: ${response.data.errmsg}`);
  }
}
```

## 总结

微信小程序的登录流程相对复杂，但遵循以下原则可以确保安全性和用户体验：

1. **静默登录**：应用启动时获取 openid，建立基础登录态
2. **按需授权**：只在必要时获取用户信息
3. **安全验证**：所有关键验证都在服务器端进行
4. **状态管理**：合理管理登录态和用户信息的缓存
5. **错误处理**：完善的错误处理机制

通过合理的设计和实现，可以为用户提供流畅的登录体验，同时确保数据安全。

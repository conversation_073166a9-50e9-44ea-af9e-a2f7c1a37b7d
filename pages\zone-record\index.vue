<template>
  <div class="zone-record all">
    <wd-navbar>
      <template #left>
        <wd-picker title="水务所筛选" :columns="columns" v-model="seleteValue" @confirm="handleConfirm" />
      </template>
      <template #right>
        <wd-search v-model="keywordName" hide-cancel placeholder-left @search="searchKeywordName" />
      </template>
    </wd-navbar>

    <!-- 统计概览卡片 -->
    <div class="stats-overview">
      <div class="stats-card">
        <div class="stats-header">
          <div class="stats-icon">
            <image class="icon-img" src="/static/img/home/<USER>"></image>
          </div>
          <div class="stats-info">
            <h3 class="stats-title">小区档案</h3>
            <p class="stats-subtitle">共 {{ total }} 个小区</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 小区列表 -->
    <div class="record-content-wrapper">
      <div class="record_content f-1 overflow-auto">
        <template v-for="(item, index) in recordList" :key="index + item.Xqbm">
          <div @click="ItemClick(item)" class="zone-card" :class="{ 'card-even': index % 2 === 0 }">
            <div class="card-content">
              <div class="card-left">
                <div class="zone-icon">
                  <div class="icon-wrapper">🏘️</div>
                </div>
                <div class="zone-info">
                  <h4 class="zone-name">{{ item.Xqmc }}</h4>
                  <p class="zone-code">编码: {{ item.Xqbm }}</p>
                </div>
              </div>
              <div class="card-right">
                <div class="water-dept">
                  <span class="dept-label">水务所</span>
                  <span class="dept-name">{{ item.Sws }}</span>
                </div>
                <div class="arrow-icon">
                  <div class="arrow">→</div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <div v-if="recordList.length === 0" class="all f-xy-center">
          <wd-status-tip image="search" tip="当前搜索无结果" />
        </div>
      </div>
    </div>

    <wd-pagination v-model="currentPagesNum" :total="total" :pageSize="pageSize" @change="handlePaginationChange" show-icon />
  </div>
  <wd-toast />
  <wd-message-box />
</template>

<script setup>
import { ref, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { zoneRecordList, getFuzzySearch } from '@/services/model/zone.record'
import * as CommonApi from '@/services/model/common'
import { useMessage, useToast } from 'wot-design-uni'

const toast = useToast()
const currentPagesNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const keywordName = ref('')
const columns = ref(['全部', '梅林', '福东', '福中', '香蜜'])
const seleteValue = ref('全部')
const message = useMessage()

// onLoad(() => getZoneRecordList(1))
onShow(async () => {
  const { data } = await CommonApi.verifyToken()
  if (data) return getZoneRecordList(1)
  recordList.value = []
  message.confirm({ msg: '该页面必须内部员工可查看是否前往登录？', title: '您还未登录' }).then(() => uni.navigateTo({ url: '/pages/login/index?redirect=1' }))
})
const recordList = ref([])

async function getZoneRecordList(PageNumber, sws) {
  try {
    toast.loading('正在加载...')
    const { Data, Page } = await zoneRecordList(PageNumber, sws)
    total.value = Page.TotalCount
    recordList.value = Data
    toast.close()
  } catch (error) {
    toast.close()
  }
}

// 页码改变
function handlePaginationChange({ value }) {
  getZoneRecordList(value)
}

// 模糊搜索
async function searchKeywordName({ value }) {
  try {
    toast.loading('正在加载...')
    const res = await getFuzzySearch(value)
    recordList.value = res.map((item) => ({ Sws: item.sws, Xqmc: item.xqmc, Xqbm: item.xqbm }))
    total.value = recordList.value.length
    currentPagesNum.value = 1
    seleteValue.value = '全部'
    toast.close()
  } catch (error) {
    toast.close()
    toast.error(error.message)
  }
}

watch(keywordName, (value) => {
  if (value === '') {
    getZoneRecordList(1)
    currentPagesNum.value = 1
    seleteValue.value = '全部'
  }
})

function ItemClick(item) {
  uni.navigateTo({ url: `/pages/zone-record/detail?sws=${item.Sws}&xqmc=${item.Xqmc}&xqbm=${item.Xqbm}` })
}

function handleConfirm({ value }) {
  if (value === '全部') {
    getZoneRecordList(1)
  } else {
    getZoneRecordList(1, value + '水务所')
  }
  currentPagesNum.value = 1
}
</script>

<style lang="less" scoped>
.zone-record {
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  height: 100vh;
}

// 统计概览样式
.stats-overview {
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #e8f4fd;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6rpx;
    background: linear-gradient(135deg, #49a2de 0%, #667eea 100%);
  }
}

.stats-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  position: relative;

  .stats-icon {
    width: 64rpx;
    height: 64rpx;
    background: linear-gradient(135deg, #49a2de 0%, #667eea 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 16rpx rgba(73, 162, 222, 0.4);
    margin-right: 24rpx;

    .icon-img {
      width: 32rpx;
      height: 32rpx;
      filter: brightness(0) invert(1);
    }
  }

  .stats-info {
    flex: 1;

    .stats-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 8rpx 0;
      line-height: 1.2;
    }

    .stats-subtitle {
      font-size: 24rpx;
      color: #7f8c8d;
      margin: 0;
      line-height: 1.3;
    }
  }
}

// 列表容器包装器
.record-content-wrapper {
  flex: 1;
  overflow: hidden;
  min-height: 0; // 重要：确保flex子元素可以收缩

  .record_content {
    padding: 0 16rpx 16rpx 16rpx;
    height: 100%;
    overflow-y: auto; // 确保内容可以滚动
  }
}

// 小区卡片样式
.zone-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f4f8;
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6rpx;
    background: linear-gradient(135deg, #49a2de 0%, #667eea 100%);
    transition: all 0.3s ease;
  }

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12), 0 4rpx 16rpx rgba(73, 162, 222, 0.08);

    &::before {
      width: 8rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }

  &:active {
    transform: translateY(-2rpx) scale(0.98);
  }

  &.card-even {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  }

  .card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    position: relative;
  }

  .card-left {
    display: flex;
    align-items: center;
    flex: 1;

    .zone-icon {
      margin-right: 24rpx;

      .icon-wrapper {
        width: 64rpx;
        height: 64rpx;
        background: linear-gradient(135deg, rgba(73, 162, 222, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
        border: 2rpx solid rgba(73, 162, 222, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        transition: all 0.3s ease;
      }
    }

    .zone-info {
      flex: 1;

      .zone-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 8rpx 0;
        line-height: 1.2;
      }

      .zone-code {
        font-size: 24rpx;
        color: #7f8c8d;
        margin: 0;
        line-height: 1.3;
      }
    }
  }

  .card-right {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .water-dept {
      text-align: right;

      .dept-label {
        display: block;
        font-size: 20rpx;
        color: #95a5a6;
        margin-bottom: 4rpx;
        line-height: 1.2;
      }

      .dept-name {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #49a2de;
        line-height: 1.2;
      }
    }

    .arrow-icon {
      .arrow {
        font-size: 32rpx;
        color: #bdc3c7;
        transition: all 0.3s ease;
      }
    }
  }

  &:hover {
    .zone-icon .icon-wrapper {
      background: linear-gradient(135deg, rgba(73, 162, 222, 0.2) 0%, rgba(102, 126, 234, 0.2) 100%);
      border-color: rgba(73, 162, 222, 0.4);
      transform: scale(1.05) rotate(5deg);
    }

    .arrow-icon .arrow {
      color: #49a2de;
      transform: translateX(4rpx);
    }
  }
}

// 动画效果
@keyframes cardShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes iconPulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>

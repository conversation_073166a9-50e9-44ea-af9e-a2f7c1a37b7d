import { cache } from '@/utils/cache.js'
class WXReauest {
  #basConfig
  constructor(config) {
    this.#basConfig = config
  }
  request(config) {
    return new Promise((resolve, reject) => {
      const token = cache.get('token')
      const header = {}
      header.Authorization = token ? 'Bearer ' + token : null

      wx.request({
        ...this.#basConfig,
        ...config,
        header,
        url: this.#basConfig.baseUrl + config.url,
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.code === 401) uni.navigateTo({ url: '/pages/login/index?redirect=1' })
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: (err) => reject(err)
      })
    })
  }

  get(url, config) {
    if (config?.params) {
      const query = Object.keys(config.params)
        .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(config.params[key])}`)
        .join('&')
      url += (url.includes('?') ? '&' : '?') + query
    }
    return this.request({ url, method: 'GET', ...config })
  }
  post(url, data, config) {
    return this.request({ url, method: 'POST', data, ...config })
  }
  put(url, config) {
    return this.request({ url, method: 'PUT', ...config })
  }
  delete(url, config) {
    return this.request({ url, method: 'DELETE', ...config })
  }
}

export default WXReauest

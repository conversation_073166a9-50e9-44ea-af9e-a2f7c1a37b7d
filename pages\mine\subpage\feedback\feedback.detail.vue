<template>
  <div class="flex f-column all relative">
    <div class="f-1 overflow-auto">
      <scroll-view class="all" scroll-y @scrolltolower="scrollIntoViewId = null" :scroll-into-view="scrollIntoViewId">
        <div class="pad-24">
          <div v-if="detail">
            <div class="text-center mar-B16 fon-S24 color-666">{{ detail.CreateTime.slice(0, 16).replace('T', ' ') }}</div>
            <div class="border-R16 pad-32 back-white box-shadow">
              <div class="fon-W600 fon-S40 mar-B12">{{ detail.FeedbackType }}</div>
              <div class="fon-S28">{{ detail.FeedbackContent }}</div>
              <div class="mar-T16 flex" v-if="detail.FeedbackImg.length > 0">
                <template v-for="(url, index) in detail.FeedbackImg.split(',')" :key="index">
                  <image class="img mar-L12" @click="previewImage(detail.FeedbackImg.split(','), url)" v-if="url" :src="url"></image>
                </template>
              </div>
            </div>

            <div class="mar-T24 border-R16 pad-32 back-white box-shadow">
              <div class="color-666 fon-S26 mar-B16">管理员回复</div>
              <div>已经收到您的反馈，我们会尽快回复的哦！</div>
            </div>
          </div>

          <template v-for="(item, index) in replyList" :key="index">
            <div class="mar-Y24" @click="handleFeedbackClick(item)">
              <div class="fon-S24 text-center color-666">{{ item.CreateTime.slice(0, 16).replace('T', ' ') }}</div>
              <div class="flex" :class="{ oneself: item.createUserInfo.id == userInfo.id }">
                <div v-if="item.createUserInfo.id !== userInfo.id">
                  <div class="user_box mar-T16">
                    <wd-icon name="user" size="22px"></wd-icon>
                  </div>
                </div>
                <div class="flex f-column">
                  <div class="fon-S22 pad-12 inline-block color-666">{{ item.createUserInfo.id !== userInfo.id ? item.createUserInfo.Name : '' }}</div>
                  <div class="text_box inline-block back-white">
                    <div class="mar-T16 flex mar-B16" v-if="item.ReplyImg.length > 0">
                      <template v-for="(url, index) in item.ReplyImg.split(',')" :key="index">
                        <image class="img mar-L12" @click="previewImage(item.ReplyImg.split(','), url)" v-if="url" :src="url"></image>
                      </template>
                    </div>
                    <div class="fon-S28">
                      {{ item.ReplyContent }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div style="height: 10px" id="bottomID"></div>
      </scroll-view>
    </div>

    <div class="input_but box-shadow back-white absolute">
      <div class="flex mar-B16" v-if="images.length > 0">
        <template v-for="(url, index) in images" :key="index">
          <div class="img_box relative">
            <div class="absolute img_icon">
              <wd-icon @click="images.splice(index, 1)" name="close-circle-filled" color="#fc3434" size="18px"></wd-icon>
            </div>
            <image class="img" :src="url" @click="previewImage(images, url)" mode="aspectFill"></image>
          </div>
        </template>
      </div>
      <div class="flex">
        <div class="flex f-column-reverse">
          <div class="input_but1 back-eee pad-16 border-Rall">
            <UploadImage url="https://www.szwgft.cn/nodeServer/resource/upload/feedback" @success="handleUploadSuccess">
              <wd-icon name="picture" size="22px" color="#333"></wd-icon>
            </UploadImage>
          </div>
        </div>
        <div class="f-1 back-eee border-R26 mar-X24 pad-12"><wd-textarea :cursor-spacing="30" v-model="reply.ReplyContent" auto-height /></div>
        <div class="flex f-column-reverse">
          <div class="input_but1 back-eee pad-16 border-Rall">
            <wd-icon name="check-bold" @click="createFeedbackIssueReply" size="22px" color="#333"></wd-icon>
          </div>
        </div>
      </div>
    </div>
    <div class="input_but back-white flex">
      <div class="flex f-column-reverse"><wd-icon name="picture" class="input_but1 back-eee pad-16 border-Rall" size="22px" color="#333"></wd-icon></div>
      <div class="f-1 back-eee border-R26 mar-X24 pad-12"></div>
      <div class="flex f-column-reverse"><wd-icon class="input_but1 back-eee pad-16 border-Rall" name="chat1" size="22px" color="#333"></wd-icon></div>
    </div>
  </div>
  <wd-toast />
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'
import { cache } from '@/utils/cache.js'
import UploadImage from '@/components/UploadeImage/index.vue'

import { feedbackIssueDetailApi, feedbackIssueReplyListApi, feedbackIssueReplyApi, feedbackReplyReadApi } from '@/services/model/feedback.issue.js'
import { dictionaryLookup } from '@/services/model/common.js'

const toast = useToast()
const ID = ref(null)
const scrollIntoViewId = ref(null)

const userInfo = JSON.parse(cache.get('userInfo'))[0]
onLoad((e) => {
  ID.value = e.id
  getFeedbackIssueList()
})
onShow(() => getFeedbackIssueDetail())

const detail = ref(null)
async function getFeedbackIssueDetail() {
  const { data } = await feedbackIssueDetailApi(ID.value, userInfo.id)
  const res = await dictionaryLookup('feedback')
  data.FeedbackType = res.data.find((item) => item.DictCode == data.FeedbackType)?.DictValue
  detail.value = data
}

// 回复列表
const replyList = ref(null)
async function getFeedbackIssueList() {
  const { data } = await feedbackIssueReplyListApi(ID.value)
  replyList.value = data
    .map((item) => {
      item.createUserInfo = JSON.parse(item.createUserInfo)[0]
      return item
    })
    .reverse()
  nextTick(() => (scrollIntoViewId.value = 'bottomID'))

  if (data.length) {
    await feedbackReplyReadApi({ UserID: userInfo.id, ReplyID: data[0].ReplyID })
  }
}

const reply = reactive({ FeedbackID: null, ReplyContent: null, ReplyUserID: null, ReplyImg: '' })

async function createFeedbackIssueReply() {
  if (!reply.ReplyContent) return toast.warning('请输入内容')
  reply.FeedbackID = ID.value
  reply.ReplyUserID = userInfo.id
  reply.ReplyImg = images.value.join(',')
  await feedbackIssueReplyApi(reply)
  reply.ReplyContent = null
  reply.ReplyImg = ''
  images.value = []
  getFeedbackIssueList()
}

function previewImage(urls, current) {
  uni.previewImage({ urls, current })
}

const images = ref([])
function handleUploadSuccess(e) {
  images.value.push(e.data)
}
</script>

<style lang="less" scoped>
.img {
  width: 100rpx;
  height: 100rpx;
}

.input_but {
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx 36rpx;
  :deep(.wd-textarea) {
    background-color: transparent;
    .wd-textarea__value {
      background-color: transparent;
    }
  }
  :deep(.wd-textarea.is-auto-height)::after {
    display: none;
  }
  :deep(.uni-textarea-textarea) {
    min-height: 80rpx !important;
    // line-height: 180rpx;
  }
  :deep(.wd-textarea.is-auto-height[data-v-324b46d3]:not(.is-cell)) {
    padding: 0;
  }
}
.input_but1 {
  display: inline-block;
}

.text_box {
  display: inline-block;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  max-width: 480rpx;
}
.user_box {
  background-color: #fff;
  display: inline-block;
  padding: 12rpx;
  border-radius: 100%;
  margin-right: 20rpx;
}

.inline-block {
  display: inline-block;
}
.oneself {
  flex-direction: row-reverse;
  .user_box {
    margin-left: 20rpx !important;
  }
  .text_box {
    background-color: #95ec69;
  }
}

.img_box {
  width: 90rpx;
  height: 90rpx;
  margin: 0 12rpx;
  border: 2rpx solid #e5e5e5;
  .img {
    width: 90rpx;
    height: 90rpx;
  }
  .img_icon {
    right: -15rpx;
    top: -10rpx;
    z-index: 9;
  }
}
</style>

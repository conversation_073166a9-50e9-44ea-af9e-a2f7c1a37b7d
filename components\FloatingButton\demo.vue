<template>
  <view class="demo-container">
    <view class="demo-section">
      <text class="demo-title">固定位置悬浮按钮</text>
      <FloatingButton
        :visible="true"
        :draggable="false"
        position="right-bottom"
        icon="add"
        @click="handleFixedClick"
      />
    </view>

    <view class="demo-section">
      <text class="demo-title">可拖拽悬浮按钮</text>
      <FloatingButton
        :visible="true"
        :draggable="true"
        :initial-position="{ x: 100, y: 200 }"
        :bounds="{ top: 100, right: 50, bottom: 100, left: 50 }"
        icon="move"
        background-color="#ff6b6b"
        @click="handleDragClick"
        @position-change="handlePositionChange"
        @drag-start="handleDragStart"
        @drag-end="handleDragEnd"
      />
    </view>

    <view class="demo-section">
      <text class="demo-title">自定义大小悬浮按钮</text>
      <FloatingButton
        :visible="true"
        :draggable="false"
        position="left-bottom"
        :size="150"
        icon="star"
        background-color="#4ecdc4"
        @click="handleCustomClick"
      />
    </view>

    <view class="demo-section">
      <text class="demo-title">禁用状态悬浮按钮</text>
      <FloatingButton
        :visible="true"
        :draggable="false"
        :disabled="true"
        position="left-top"
        icon="close"
        @click="handleDisabledClick"
      />
    </view>

    <!-- 日志显示 -->
    <view class="log-container">
      <text class="log-title">事件日志:</text>
      <view class="log-item" v-for="(log, index) in logs" :key="index">
        <text class="log-text">{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import FloatingButton from './index.vue'

const logs = ref([])

function addLog(message) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 10) {
    logs.value.pop()
  }
}

function handleFixedClick() {
  addLog('固定位置按钮被点击')
  uni.showToast({
    title: '固定按钮点击',
    icon: 'success'
  })
}

function handleDragClick() {
  addLog('可拖拽按钮被点击')
  uni.showToast({
    title: '拖拽按钮点击',
    icon: 'success'
  })
}

function handleCustomClick() {
  addLog('自定义大小按钮被点击')
  uni.showToast({
    title: '自定义按钮点击',
    icon: 'success'
  })
}

function handleDisabledClick() {
  addLog('禁用按钮被点击（不应该触发）')
}

function handlePositionChange(position) {
  addLog(`按钮位置变化: x=${position.x}, y=${position.y}`)
}

function handleDragStart() {
  addLog('开始拖拽')
}

function handleDragEnd() {
  addLog('结束拖拽')
}
</script>

<style lang="less" scoped>
.demo-container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.demo-section {
  margin-bottom: 60rpx;
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.demo-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.log-container {
  margin-top: 40rpx;
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  max-height: 600rpx;
  overflow-y: auto;
}

.log-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.log-item {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.log-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}
</style>
